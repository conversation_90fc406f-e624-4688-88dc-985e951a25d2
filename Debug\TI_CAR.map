******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jul 30 12:32:12 2025

OUTPUT FILE NAME:   <TI_CAR.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00007329


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000096f8  00016908  R  X
  SRAM                  20200000   00008000  000006b1  0000794f  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000096f8   000096f8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00007ee0   00007ee0    r-x .text
  00007fa0    00007fa0    000016d0   000016d0    r-- .rodata
  00009670    00009670    00000088   00000088    r-- .cinit
20200000    20200000    000004b2   00000000    rw-
  20200000    20200000    00000323   00000000    rw- .bss
  20200324    20200324    0000018e   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00007ee0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    000002b0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000139c    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001614    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  0000184c    0000022c     MPU6050.o (.text.Read_Quad)
                  00001a78    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  00001ca4    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001ec4    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  000020b8    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00002294    000001b0     Task.o (.text.Task_Start)
                  00002444    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  000025e4    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00002776    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002778    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  00002900    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002a78    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002be8    00000168     Interrupt.o (.text.GROUP1_IRQHandler)
                  00002d50    00000144     MPU6050.o (.text.MPU6050_Init)
                  00002e94    0000013c     Tracker.o (.text.Tracker_Read)
                  00002fd0    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  0000310c    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  00003240    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00003374    00000130     OLED.o (.text.OLED_ShowChar)
                  000034a4    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  000035d4    00000128     inv_mpu.o (.text.mpu_init)
                  000036fc    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  00003820    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  00003944    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00003a64    00000110     OLED.o (.text.OLED_Init)
                  00003b74    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003c80    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003d88    00000104     Task_App.o (.text.Task_Motor_PID)
                  00003e8c    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00003f8c    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  00004078    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  0000415c    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  00004240    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  0000431c    000000dc     Task_App.o (.text.Task_OLED)
                  000043f8    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000044d0    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000045a8    000000d4     inv_mpu.o (.text.set_int_enable)
                  0000467c    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  0000474c    000000c4     driverlib.a : dl_timer.o (.text.DL_Timer_initPWMMode)
                  00004810    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  000048d4    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00004998    000000bc     Motor.o (.text.Motor_Start)
                  00004a54    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  00004b10    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00004bc8    000000b4     Task.o (.text.Task_Add)
                  00004c7c    000000b0     Task_App.o (.text.Task_Init)
                  00004d2c    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00004dd8    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  00004e84    000000a4     Motor.o (.text.Motor_GetSpeed)
                  00004f28    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00004fca    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00004fcc    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  0000506c    0000009c     Motor.o (.text.Motor_SetDuty)
                  00005108    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  000051a4    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  0000523c    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  000052d4    00000096     MPU6050.o (.text.inv_row_2_scale)
                  0000536a    00000002     --HOLE-- [fill = 0]
                  0000536c    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000053f8    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00005484    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00005508    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  0000558c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0000560e    00000002     --HOLE-- [fill = 0]
                  00005610    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBack_init)
                  00005690    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  00005710    00000080     Task_App.o (.text.Task_Serial)
                  00005790    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  0000580c    00000074     Motor.o (.text.Motor_SetDirc)
                  00005880    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000058f4    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00005900    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005974    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  000059e8    00000070     Serial.o (.text.MyPrintf_DMA)
                  00005a58    0000006e     OLED.o (.text.OLED_ShowString)
                  00005ac6    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00005b30    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00005b98    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00005bfe    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00005c64    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00005cc8    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00005d2c    00000064     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00005d90    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00005df2    00000002     --HOLE-- [fill = 0]
                  00005df4    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00005e56    00000002     --HOLE-- [fill = 0]
                  00005e58    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00005eb8    00000060     Key_Led.o (.text.Key_Read)
                  00005f18    00000060     Task_App.o (.text.Task_IdleFunction)
                  00005f78    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00005fd8    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00006038    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00006096    00000002     --HOLE-- [fill = 0]
                  00006098    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000060f4    0000005c     Task_App.o (.text.Task_Tracker)
                  00006150    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  000061ac    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00006204    00000058     Serial.o (.text.Serial_Init)
                  0000625c    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  000062b4    00000058            : _printfi.c.obj (.text._pconv_f)
                  0000630c    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00006362    00000002     --HOLE-- [fill = 0]
                  00006364    00000054     Interrupt.o (.text.Interrupt_Init)
                  000063b8    00000054     MPU6050.o (.text.mspm0_i2c_enable)
                  0000640c    00000054     OLED.o (.text.mspm0_i2c_enable)
                  00006460    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  000064b2    00000002     --HOLE-- [fill = 0]
                  000064b4    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  00006504    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00006554    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000065a4    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  000065f0    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  0000663c    0000004c     OLED.o (.text.OLED_Printf)
                  00006688    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000066d2    00000002     --HOLE-- [fill = 0]
                  000066d4    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  0000671c    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  00006764    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  000067ac    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000067f0    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  00006834    00000044     Task_App.o (.text.Task_Key)
                  00006878    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  000068bc    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  00006900    00000044     MPU6050.o (.text.mspm0_i2c_disable)
                  00006944    00000044     OLED.o (.text.mspm0_i2c_disable)
                  00006988    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  000069ca    00000002     --HOLE-- [fill = 0]
                  000069cc    00000040                            : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00006a0c    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00006a4c    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00006a8c    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00006acc    0000003e     Task.o (.text.Task_CMP)
                  00006b0a    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  00006b48    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006b84    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006bc0    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006bfc    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00006c38    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00006c74    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00006cb0    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00006cec    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00006d28    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00006d62    00000002     --HOLE-- [fill = 0]
                  00006d64    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00006d9e    00000002     --HOLE-- [fill = 0]
                  00006da0    00000038     Task_App.o (.text.Task_LED)
                  00006dd8    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00006e10    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006e44    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006e78    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006eac    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  00006ee0    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  00006f12    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  00006f44    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00006f74    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00006fa4    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00006fd4    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00007004    00000030            : vsnprintf.c.obj (.text._outs)
                  00007034    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  00007064    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00007094    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000070c0    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  000070ec    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00007118    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00007144    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  0000716e    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00007196    00000028     OLED.o (.text.DL_Common_updateReg)
                  000071be    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000071e6    00000002     --HOLE-- [fill = 0]
                  000071e8    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00007210    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00007238    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00007260    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00007288    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  000072b0    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  000072d8    00000028     SysTick.o (.text.SysTick_Increasment)
                  00007300    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00007328    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00007350    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00007376    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  0000739c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  000073c2    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000073e8    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  0000740c    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00007430    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00007452    00000002     --HOLE-- [fill = 0]
                  00007454    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00007474    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00007494    00000020     SysTick.o (.text.Delay)
                  000074b4    00000020     main.o (.text.main)
                  000074d4    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  000074f4    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00007512    00000002     --HOLE-- [fill = 0]
                  00007514    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00007532    00000002     --HOLE-- [fill = 0]
                  00007534    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00007550    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  0000756c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00007588    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  000075a4    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  000075c0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  000075dc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000075f8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00007614    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00007630    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  0000764c    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00007668    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00007684    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  000076a0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000076bc    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  000076d8    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000076f4    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00007710    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  0000772c    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00007748    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00007760    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00007778    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00007790    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000077a8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000077c0    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000077d8    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  000077f0    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00007808    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00007820    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00007838    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00007850    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  00007868    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00007880    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00007898    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  000078b0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000078c8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000078e0    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  000078f8    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00007910    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00007928    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00007940    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00007958    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  00007970    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00007988    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000079a0    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  000079b8    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  000079d0    00000018     MPU6050.o (.text.DL_I2C_reset)
                  000079e8    00000018     OLED.o (.text.DL_I2C_reset)
                  00007a00    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00007a18    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00007a30    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00007a48    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00007a60    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00007a78    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00007a90    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00007aa8    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00007ac0    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00007ad8    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  00007af0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00007b08    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00007b20    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00007b38    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00007b50    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00007b68    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00007b80    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00007b98    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  00007bb0    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00007bc8    00000018            : vsprintf.c.obj (.text._outs)
                  00007be0    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00007bf6    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  00007c0c    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00007c22    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00007c38    00000016     Tracker.o (.text.DL_GPIO_readPins)
                  00007c4e    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00007c64    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00007c7a    00000016     SysTick.o (.text.SysGetTick)
                  00007c90    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00007ca6    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00007cba    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00007cce    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00007ce2    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  00007cf6    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00007d0a    00000002     --HOLE-- [fill = 0]
                  00007d0c    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00007d20    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00007d34    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00007d48    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00007d5c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00007d70    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00007d84    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00007d98    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00007dac    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00007dc0    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00007dd4    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00007de8    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00007dfa    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00007e0c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00007e1e    00000002     --HOLE-- [fill = 0]
                  00007e20    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00007e30    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00007e40    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00007e50    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00007e60    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00007e6e    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00007e7c    0000000e     MPU6050.o (.text.tap_cb)
                  00007e8a    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00007e98    0000000c     SysTick.o (.text.Sys_GetTick)
                  00007ea4    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00007eae    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007eb8    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00007ec8    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007ed2    00000002     --HOLE-- [fill = 0]
                  00007ed4    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00007ee4    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007eee    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007ef8    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007f02    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00007f0c    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00007f1c    0000000a     libc.a : vsprintf.c.obj (.text._outc)
                  00007f26    0000000a     MPU6050.o (.text.android_orient_cb)
                  00007f30    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007f38    00000008     Interrupt.o (.text.SysTick_Handler)
                  00007f40    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00007f48    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00007f50    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007f56    00000002     --HOLE-- [fill = 0]
                  00007f58    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00007f68    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007f6e    00000006            : exit.c.obj (.text:abort)
                  00007f74    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00007f78    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00007f7c    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00007f80    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00007f84    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00007f94    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00007f98    00000008     --HOLE-- [fill = 0]

.cinit     0    00009670    00000088     
                  00009670    00000062     (.cinit..data.load) [load image, compression = lzss]
                  000096d2    00000002     --HOLE-- [fill = 0]
                  000096d4    0000000c     (__TI_handler_table)
                  000096e0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000096e8    00000010     (__TI_cinit_table)

.rodata    0    00007fa0    000016d0     
                  00007fa0    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00008b96    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00009186    00000228     OLED_Font.o (.rodata.asc2_0806)
                  000093ae    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  000093b0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  000094b1    00000007     Task_App.o (.rodata.str1.8896853068034818020.1)
                  000094b8    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  000094f8    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00009520    00000028     inv_mpu.o (.rodata.test)
                  00009548    0000001e     inv_mpu.o (.rodata.reg)
                  00009566    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00009568    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00009580    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00009598    00000014     Task_App.o (.rodata.str1.11952760121962574671.1)
                  000095ac    00000014     Task_App.o (.rodata.str1.14074990341397557290.1)
                  000095c0    00000014     Task_App.o (.rodata.str1.492715258893803702.1)
                  000095d4    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  000095e5    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  000095f6    00000011     Task_App.o (.rodata.str1.3850258909703972507.1)
                  00009607    00000011     Task_App.o (.rodata.str1.5883415095785080416.1)
                  00009618    0000000c     inv_mpu.o (.rodata.hw)
                  00009624    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  0000962e    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00009630    00000008     ti_msp_dl_config.o (.rodata.gMotorBackConfig)
                  00009638    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  00009640    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00009648    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  0000964e    00000005     Task_App.o (.rodata.str1.11683036942922059812.1)
                  00009653    00000004     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00009657    00000004     Task_App.o (.rodata.str1.16020955549137178199.1)
                  0000965b    00000003     ti_msp_dl_config.o (.rodata.gMotorBackClockConfig)
                  0000965e    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  00009661    0000000f     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000323     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    00000010     (.common:quat)
                  20200300    00000006     (.common:Data_Accel)
                  20200306    00000006     (.common:Data_Gyro)
                  2020030c    00000004     (.common:Data_Pitch)
                  20200310    00000004     (.common:Data_Roll)
                  20200314    00000004     (.common:Data_Yaw)
                  20200318    00000004     (.common:ExISR_Flag)
                  2020031c    00000004     (.common:sensor_timestamp)
                  20200320    00000002     (.common:sensors)
                  20200322    00000001     (.common:more)

.data      0    20200324    0000018e     UNINITIALIZED
                  20200324    00000040     Motor.o (.data.Motor_Back_Left)
                  20200364    00000040     Motor.o (.data.Motor_Back_Right)
                  202003a4    00000040     Motor.o (.data.Motor_Font_Left)
                  202003e4    00000040     Motor.o (.data.Motor_Font_Right)
                  20200424    0000002c     inv_mpu.o (.data.st)
                  20200450    00000010     Task_App.o (.data.Motor)
                  20200460    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  20200470    0000000e     MPU6050.o (.data.hal)
                  2020047e    00000009     MPU6050.o (.data.gyro_orientation)
                  20200487    00000001     Task_App.o (.data.Flag_LED)
                  20200488    00000008     Task_App.o (.data.Data_MotorEncoder)
                  20200490    00000008     Task_App.o (.data.Data_Tracker_Input)
                  20200498    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  2020049c    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202004a0    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004a4    00000004     SysTick.o (.data.delayTick)
                  202004a8    00000004     SysTick.o (.data.uwTick)
                  202004ac    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202004ae    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  202004af    00000001     Task_App.o (.data.Task_Key.Key_Old)
                  202004b0    00000001     Task.o (.data.Task_Num)
                  202004b1    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3394    126       0      
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3434    318       0      
                                                               
    .\APP\Src\
       Task_App.o                     1140    128       44     
       Interrupt.o                    622     0         6      
    +--+------------------------------+-------+---------+---------+
       Total:                         1762    128       50     
                                                               
    .\BSP\Src\
       MPU6050.o                      2460    0         70     
       OLED_Font.o                    0       2072      0      
       OLED.o                         1846    0         0      
       Motor.o                        692     0         256    
       Serial.o                       404     0         512    
       Task.o                         674     0         241    
       PID_IQMath.o                   402     0         0      
       Tracker.o                      338     0         0      
       Key_Led.o                      118     0         0      
       SysTick.o                      106     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         7040    2072      1087   
                                                               
    .\DMP\
       inv_mpu_dmp_motion_driver.o    3110    3062      16     
       inv_mpu.o                      4600    82        44     
    +--+------------------------------+-------+---------+---------+
       Total:                         7710    3144      60     
                                                               
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     292     0         0      
       dl_i2c.o                       192     0         0      
       dl_uart.o                      90      0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1048    0         0      
                                                               
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/iqmath/lib/ticlang/m0p/mathacl/iqmath.a
       _IQNtoF.o                      48      0         0      
       _IQNdiv.o                      24      0         0      
       _IQNmpy.o                      24      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         96      0         0      
                                                               
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       vsnprintf.c.obj                136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       memcmp.c.obj                   32      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         8356    355       4      
                                                               
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2984    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       134       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   32434   6151      1713   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000096e8 records: 2, size/record: 8, table size: 16
	.data: load addr=00009670, load size=00000062 bytes, run addr=20200324, run size=0000018e bytes, compression=lzss
	.bss: load addr=000096e0, load size=00000008 bytes, run addr=20200000, run size=00000323 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000096d4 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000025e5     00007eb8     00007eb6   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00004079     00007ed4     00007ed0   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007eec          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007f00          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007f36          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00007f6c          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00003b75     00007f0c     00007f0a   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   000025ef     00007f58     00007f54   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007f7e          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00007329     00007f84     00007f80   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00007f75  ADC0_IRQHandler                      
00007f75  ADC1_IRQHandler                      
00007f75  AES_IRQHandler                       
00007f78  C$$EXIT                              
00007f75  CANFD0_IRQHandler                    
00007f75  DAC0_IRQHandler                      
00007ea5  DL_Common_delayCycles                
000065a5  DL_DMA_initChannel                   
00006039  DL_I2C_fillControllerTXFIFO          
00006bfd  DL_I2C_flushControllerTXFIFO         
000073c3  DL_I2C_setClockConfig                
00004241  DL_SYSCTL_configSYSPLL               
00005c65  DL_SYSCTL_setHFCLKSourceHFXTParams   
000067ad  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000474d  DL_Timer_initPWMMode                 
000076d9  DL_Timer_setCaptCompUpdateMethod     
00007aa9  DL_Timer_setCaptureCompareOutCtl     
00007e31  DL_Timer_setCaptureCompareValue      
000076f5  DL_Timer_setClockConfig              
000066d5  DL_UART_init                         
00007de9  DL_UART_setClockConfig               
00007f75  DMA_IRQHandler                       
20200300  Data_Accel                           
20200306  Data_Gyro                            
20200488  Data_MotorEncoder                    
20200498  Data_Motor_TarSpeed                  
2020030c  Data_Pitch                           
20200310  Data_Roll                            
20200490  Data_Tracker_Input                   
2020049c  Data_Tracker_Offset                  
20200314  Data_Yaw                             
00007f75  Default_Handler                      
00007495  Delay                                
20200318  ExISR_Flag                           
20200487  Flag_LED                             
202004ae  Flag_MPU6050_Ready                   
00007f75  GROUP0_IRQHandler                    
00002be9  GROUP1_IRQHandler                    
00007f79  HOSTexit                             
00007f75  HardFault_Handler                    
00007f75  I2C0_IRQHandler                      
00007f75  I2C1_IRQHandler                      
00005ac7  I2C_OLED_Clear                       
00006c39  I2C_OLED_Set_Pos                     
000051a5  I2C_OLED_WR_Byte                     
00005e59  I2C_OLED_i2c_sda_unlock              
00006365  Interrupt_Init                       
00005eb9  Key_Read                             
00002d51  MPU6050_Init                         
20200450  Motor                                
20200324  Motor_Back_Left                      
20200364  Motor_Back_Right                     
202003a4  Motor_Font_Left                      
202003e4  Motor_Font_Right                     
00004e85  Motor_GetSpeed                       
0000506d  Motor_SetDuty                        
00004999  Motor_Start                          
000059e9  MyPrintf_DMA                         
00007f75  NMI_Handler                          
00003a65  OLED_Init                            
0000663d  OLED_Printf                          
00003375  OLED_ShowChar                        
00005a59  OLED_ShowString                      
00007145  PID_IQ_Init                          
000036fd  PID_IQ_Prosc                         
000067f1  PID_IQ_SetParams                     
00007f75  PendSV_Handler                       
00007f75  RTC_IRQHandler                       
0000184d  Read_Quad                            
00007f81  Reset_Handler                        
00007f75  SPI0_IRQHandler                      
00007f75  SPI1_IRQHandler                      
00007f75  SVC_Handler                          
00006f75  SYSCFG_DL_DMA_CH_RX_init             
00007b69  SYSCFG_DL_DMA_CH_TX_init             
000058f5  SYSCFG_DL_DMA_init                   
000010ed  SYSCFG_DL_GPIO_init                  
000061ad  SYSCFG_DL_I2C_MPU6050_init           
00005cc9  SYSCFG_DL_I2C_OLED_init              
00005611  SYSCFG_DL_MotorBack_init             
00005691  SYSCFG_DL_MotorFront_init            
00006099  SYSCFG_DL_SYSCTL_init                
00007e41  SYSCFG_DL_SYSTICK_init               
00005485  SYSCFG_DL_UART0_init                 
00007095  SYSCFG_DL_init                       
00004fcd  SYSCFG_DL_initPower                  
00006205  Serial_Init                          
20200000  Serial_RxData                        
00007c7b  SysGetTick                           
00007f39  SysTick_Handler                      
000072d9  SysTick_Increasment                  
00007e99  Sys_GetTick                          
00007f75  TIMA0_IRQHandler                     
00007f75  TIMA1_IRQHandler                     
00007f75  TIMG0_IRQHandler                     
00007f75  TIMG12_IRQHandler                    
00007f75  TIMG6_IRQHandler                     
00007f75  TIMG7_IRQHandler                     
00007f75  TIMG8_IRQHandler                     
00007dfb  TI_memcpy_small                      
00007e8b  TI_memset_small                      
00004bc9  Task_Add                             
00005f19  Task_IdleFunction                    
00004c7d  Task_Init                            
00006835  Task_Key                             
00006da1  Task_LED                             
00003d89  Task_Motor_PID                       
0000431d  Task_OLED                            
00005711  Task_Serial                          
00002295  Task_Start                           
000060f5  Task_Tracker                         
00002e95  Tracker_Read                         
00007f75  UART0_IRQHandler                     
00007f75  UART1_IRQHandler                     
00007f75  UART2_IRQHandler                     
00007f75  UART3_IRQHandler                     
00007b81  _IQ24div                             
00007b99  _IQ24mpy                             
00006fa5  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000096e8  __TI_CINIT_Base                      
000096f8  __TI_CINIT_Limit                     
000096f8  __TI_CINIT_Warm                      
000096d4  __TI_Handler_Table_Base              
000096e0  __TI_Handler_Table_Limit             
00006ced  __TI_auto_init_nobinit_nopinit       
00005791  __TI_decompress_lzss                 
00007e0d  __TI_decompress_none                 
0000625d  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00007c91  __TI_zero_init_nomemset              
000025ef  __adddf3                             
000044db  __addsf3                             
000093b0  __aeabi_ctype_table_                 
000093b0  __aeabi_ctype_table_C                
00005901  __aeabi_d2f                          
00006689  __aeabi_d2iz                         
00006989  __aeabi_d2uiz                        
000025ef  __aeabi_dadd                         
00005d91  __aeabi_dcmpeq                       
00005dcd  __aeabi_dcmpge                       
00005de1  __aeabi_dcmpgt                       
00005db9  __aeabi_dcmple                       
00005da5  __aeabi_dcmplt                       
00003b75  __aeabi_ddiv                         
00004079  __aeabi_dmul                         
000025e5  __aeabi_dsub                         
202004a0  __aeabi_errno                        
00007f41  __aeabi_errno_addr                   
00006a0d  __aeabi_f2d                          
00006dd9  __aeabi_f2iz                         
000044db  __aeabi_fadd                         
00005df5  __aeabi_fcmpeq                       
00005e31  __aeabi_fcmpge                       
00005e45  __aeabi_fcmpgt                       
00005e1d  __aeabi_fcmple                       
00005e09  __aeabi_fcmplt                       
0000558d  __aeabi_fdiv                         
0000536d  __aeabi_fmul                         
000044d1  __aeabi_fsub                         
000070ed  __aeabi_i2d                          
00006c75  __aeabi_i2f                          
0000630d  __aeabi_idiv                         
00002777  __aeabi_idiv0                        
0000630d  __aeabi_idivmod                      
00004fcb  __aeabi_ldiv0                        
00007515  __aeabi_llsl                         
0000740d  __aeabi_lmul                         
00007f49  __aeabi_memcpy                       
00007f49  __aeabi_memcpy4                      
00007f49  __aeabi_memcpy8                      
00007e61  __aeabi_memset                       
00007e61  __aeabi_memset4                      
00007e61  __aeabi_memset8                      
00007301  __aeabi_ui2f                         
000069cd  __aeabi_uidiv                        
000069cd  __aeabi_uidivmod                     
00007d99  __aeabi_uldivmod                     
00007515  __ashldi3                            
ffffffff  __binit__                            
00005b31  __cmpdf2                             
00006d29  __cmpsf2                             
00003b75  __divdf3                             
0000558d  __divsf3                             
00005b31  __eqdf2                              
00006d29  __eqsf2                              
00006a0d  __extendsfdf2                        
00006689  __fixdfsi                            
00006dd9  __fixsfsi                            
00006989  __fixunsdfsi                         
000070ed  __floatsidf                          
00006c75  __floatsisf                          
00007301  __floatunsisf                        
00005881  __gedf2                              
00006cb1  __gesf2                              
00005881  __gtdf2                              
00006cb1  __gtsf2                              
00005b31  __ledf2                              
00006d29  __lesf2                              
00005b31  __ltdf2                              
00006d29  __ltsf2                              
UNDEFED   __mpu_init                           
00004079  __muldf3                             
0000740d  __muldi3                             
00006d65  __muldsi3                            
0000536d  __mulsf3                             
00005b31  __nedf2                              
00006d29  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000025e5  __subdf3                             
000044d1  __subsf3                             
00005901  __truncdfsf2                         
00004f29  __udivmoddi4                         
00007329  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00007f95  _system_pre_init                     
00007f6f  abort                                
00009186  asc2_0806                            
00008b96  asc2_1608                            
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00002779  atan2                                
00002779  atan2l                               
00000df5  atanl                                
00006a4d  atoi                                 
ffffffff  binit                                
202004a4  delayTick                            
0000671d  dmp_enable_6x_lp_quat                
0000139d  dmp_enable_feature                   
00005f79  dmp_enable_gyro_cal                  
00006765  dmp_enable_lp_quat                   
0000772d  dmp_load_motion_driver_firmware      
00001ec5  dmp_read_fifo                        
00007dad  dmp_register_android_orient_cb       
00007dc1  dmp_register_tap_cb                  
0000523d  dmp_set_fifo_rate                    
00002901  dmp_set_orientation                  
00006879  dmp_set_shake_reject_thresh          
00006ee1  dmp_set_shake_reject_time            
00006f13  dmp_set_shake_reject_timeout         
00005bff  dmp_set_tap_axes                     
000068bd  dmp_set_tap_count                    
00001615  dmp_set_tap_thresh                   
00007035  dmp_set_tap_time                     
00007065  dmp_set_tap_time_multi               
202004b1  enable_group1_irq                    
00006151  frexp                                
00006151  frexpl                               
00009618  hw                                   
00000000  interruptVectors                     
000043f9  ldexp                                
000043f9  ldexpl                               
000074b5  main                                 
00007431  memccpy                              
000074d5  memcmp                               
20200322  more                                 
00005d2d  mpu6050_i2c_sda_unlock               
00004a55  mpu_configure_fifo                   
00005975  mpu_get_accel_fsr                    
00005fd9  mpu_get_gyro_fsr                     
00006ead  mpu_get_sample_rate                  
000035d5  mpu_init                             
00003821  mpu_load_firmware                    
00003e8d  mpu_lp_accel_mode                    
00003c81  mpu_read_fifo_stream                 
00004d2d  mpu_read_mem                         
00001a79  mpu_reset_fifo                       
0000415d  mpu_set_accel_fsr                    
00002445  mpu_set_bypass                       
00004b11  mpu_set_dmp_state                    
00004811  mpu_set_gyro_fsr                     
00005109  mpu_set_int_latched                  
0000467d  mpu_set_lpf                          
00003f8d  mpu_set_sample_rate                  
000034a5  mpu_set_sensors                      
00004dd9  mpu_write_mem                        
0000310d  mspm0_i2c_read                       
000048d5  mspm0_i2c_write                      
00003241  qsort                                
202002f0  quat                                 
00009548  reg                                  
000043f9  scalbn                               
000043f9  scalbnl                              
2020031c  sensor_timestamp                     
20200320  sensors                              
00002a79  sqrt                                 
00002a79  sqrtl                                
00009520  test                                 
202004a8  uwTick                               
00006a8d  vsnprintf                            
00007119  vsprintf                             
00007e51  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  SYSCFG_DL_GPIO_init                  
0000139d  dmp_enable_feature                   
00001615  dmp_set_tap_thresh                   
0000184d  Read_Quad                            
00001a79  mpu_reset_fifo                       
00001ec5  dmp_read_fifo                        
00002295  Task_Start                           
00002445  mpu_set_bypass                       
000025e5  __aeabi_dsub                         
000025e5  __subdf3                             
000025ef  __adddf3                             
000025ef  __aeabi_dadd                         
00002777  __aeabi_idiv0                        
00002779  atan2                                
00002779  atan2l                               
00002901  dmp_set_orientation                  
00002a79  sqrt                                 
00002a79  sqrtl                                
00002be9  GROUP1_IRQHandler                    
00002d51  MPU6050_Init                         
00002e95  Tracker_Read                         
0000310d  mspm0_i2c_read                       
00003241  qsort                                
00003375  OLED_ShowChar                        
000034a5  mpu_set_sensors                      
000035d5  mpu_init                             
000036fd  PID_IQ_Prosc                         
00003821  mpu_load_firmware                    
00003a65  OLED_Init                            
00003b75  __aeabi_ddiv                         
00003b75  __divdf3                             
00003c81  mpu_read_fifo_stream                 
00003d89  Task_Motor_PID                       
00003e8d  mpu_lp_accel_mode                    
00003f8d  mpu_set_sample_rate                  
00004079  __aeabi_dmul                         
00004079  __muldf3                             
0000415d  mpu_set_accel_fsr                    
00004241  DL_SYSCTL_configSYSPLL               
0000431d  Task_OLED                            
000043f9  ldexp                                
000043f9  ldexpl                               
000043f9  scalbn                               
000043f9  scalbnl                              
000044d1  __aeabi_fsub                         
000044d1  __subsf3                             
000044db  __addsf3                             
000044db  __aeabi_fadd                         
0000467d  mpu_set_lpf                          
0000474d  DL_Timer_initPWMMode                 
00004811  mpu_set_gyro_fsr                     
000048d5  mspm0_i2c_write                      
00004999  Motor_Start                          
00004a55  mpu_configure_fifo                   
00004b11  mpu_set_dmp_state                    
00004bc9  Task_Add                             
00004c7d  Task_Init                            
00004d2d  mpu_read_mem                         
00004dd9  mpu_write_mem                        
00004e85  Motor_GetSpeed                       
00004f29  __udivmoddi4                         
00004fcb  __aeabi_ldiv0                        
00004fcd  SYSCFG_DL_initPower                  
0000506d  Motor_SetDuty                        
00005109  mpu_set_int_latched                  
000051a5  I2C_OLED_WR_Byte                     
0000523d  dmp_set_fifo_rate                    
0000536d  __aeabi_fmul                         
0000536d  __mulsf3                             
00005485  SYSCFG_DL_UART0_init                 
0000558d  __aeabi_fdiv                         
0000558d  __divsf3                             
00005611  SYSCFG_DL_MotorBack_init             
00005691  SYSCFG_DL_MotorFront_init            
00005711  Task_Serial                          
00005791  __TI_decompress_lzss                 
00005881  __gedf2                              
00005881  __gtdf2                              
000058f5  SYSCFG_DL_DMA_init                   
00005901  __aeabi_d2f                          
00005901  __truncdfsf2                         
00005975  mpu_get_accel_fsr                    
000059e9  MyPrintf_DMA                         
00005a59  OLED_ShowString                      
00005ac7  I2C_OLED_Clear                       
00005b31  __cmpdf2                             
00005b31  __eqdf2                              
00005b31  __ledf2                              
00005b31  __ltdf2                              
00005b31  __nedf2                              
00005bff  dmp_set_tap_axes                     
00005c65  DL_SYSCTL_setHFCLKSourceHFXTParams   
00005cc9  SYSCFG_DL_I2C_OLED_init              
00005d2d  mpu6050_i2c_sda_unlock               
00005d91  __aeabi_dcmpeq                       
00005da5  __aeabi_dcmplt                       
00005db9  __aeabi_dcmple                       
00005dcd  __aeabi_dcmpge                       
00005de1  __aeabi_dcmpgt                       
00005df5  __aeabi_fcmpeq                       
00005e09  __aeabi_fcmplt                       
00005e1d  __aeabi_fcmple                       
00005e31  __aeabi_fcmpge                       
00005e45  __aeabi_fcmpgt                       
00005e59  I2C_OLED_i2c_sda_unlock              
00005eb9  Key_Read                             
00005f19  Task_IdleFunction                    
00005f79  dmp_enable_gyro_cal                  
00005fd9  mpu_get_gyro_fsr                     
00006039  DL_I2C_fillControllerTXFIFO          
00006099  SYSCFG_DL_SYSCTL_init                
000060f5  Task_Tracker                         
00006151  frexp                                
00006151  frexpl                               
000061ad  SYSCFG_DL_I2C_MPU6050_init           
00006205  Serial_Init                          
0000625d  __TI_ltoa                            
0000630d  __aeabi_idiv                         
0000630d  __aeabi_idivmod                      
00006365  Interrupt_Init                       
000065a5  DL_DMA_initChannel                   
0000663d  OLED_Printf                          
00006689  __aeabi_d2iz                         
00006689  __fixdfsi                            
000066d5  DL_UART_init                         
0000671d  dmp_enable_6x_lp_quat                
00006765  dmp_enable_lp_quat                   
000067ad  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000067f1  PID_IQ_SetParams                     
00006835  Task_Key                             
00006879  dmp_set_shake_reject_thresh          
000068bd  dmp_set_tap_count                    
00006989  __aeabi_d2uiz                        
00006989  __fixunsdfsi                         
000069cd  __aeabi_uidiv                        
000069cd  __aeabi_uidivmod                     
00006a0d  __aeabi_f2d                          
00006a0d  __extendsfdf2                        
00006a4d  atoi                                 
00006a8d  vsnprintf                            
00006bfd  DL_I2C_flushControllerTXFIFO         
00006c39  I2C_OLED_Set_Pos                     
00006c75  __aeabi_i2f                          
00006c75  __floatsisf                          
00006cb1  __gesf2                              
00006cb1  __gtsf2                              
00006ced  __TI_auto_init_nobinit_nopinit       
00006d29  __cmpsf2                             
00006d29  __eqsf2                              
00006d29  __lesf2                              
00006d29  __ltsf2                              
00006d29  __nesf2                              
00006d65  __muldsi3                            
00006da1  Task_LED                             
00006dd9  __aeabi_f2iz                         
00006dd9  __fixsfsi                            
00006ead  mpu_get_sample_rate                  
00006ee1  dmp_set_shake_reject_time            
00006f13  dmp_set_shake_reject_timeout         
00006f75  SYSCFG_DL_DMA_CH_RX_init             
00006fa5  _IQ24toF                             
00007035  dmp_set_tap_time                     
00007065  dmp_set_tap_time_multi               
00007095  SYSCFG_DL_init                       
000070ed  __aeabi_i2d                          
000070ed  __floatsidf                          
00007119  vsprintf                             
00007145  PID_IQ_Init                          
000072d9  SysTick_Increasment                  
00007301  __aeabi_ui2f                         
00007301  __floatunsisf                        
00007329  _c_int00_noargs                      
000073c3  DL_I2C_setClockConfig                
0000740d  __aeabi_lmul                         
0000740d  __muldi3                             
00007431  memccpy                              
00007495  Delay                                
000074b5  main                                 
000074d5  memcmp                               
00007515  __aeabi_llsl                         
00007515  __ashldi3                            
000076d9  DL_Timer_setCaptCompUpdateMethod     
000076f5  DL_Timer_setClockConfig              
0000772d  dmp_load_motion_driver_firmware      
00007aa9  DL_Timer_setCaptureCompareOutCtl     
00007b69  SYSCFG_DL_DMA_CH_TX_init             
00007b81  _IQ24div                             
00007b99  _IQ24mpy                             
00007c7b  SysGetTick                           
00007c91  __TI_zero_init_nomemset              
00007d99  __aeabi_uldivmod                     
00007dad  dmp_register_android_orient_cb       
00007dc1  dmp_register_tap_cb                  
00007de9  DL_UART_setClockConfig               
00007dfb  TI_memcpy_small                      
00007e0d  __TI_decompress_none                 
00007e31  DL_Timer_setCaptureCompareValue      
00007e41  SYSCFG_DL_SYSTICK_init               
00007e51  wcslen                               
00007e61  __aeabi_memset                       
00007e61  __aeabi_memset4                      
00007e61  __aeabi_memset8                      
00007e8b  TI_memset_small                      
00007e99  Sys_GetTick                          
00007ea5  DL_Common_delayCycles                
00007f39  SysTick_Handler                      
00007f41  __aeabi_errno_addr                   
00007f49  __aeabi_memcpy                       
00007f49  __aeabi_memcpy4                      
00007f49  __aeabi_memcpy8                      
00007f6f  abort                                
00007f75  ADC0_IRQHandler                      
00007f75  ADC1_IRQHandler                      
00007f75  AES_IRQHandler                       
00007f75  CANFD0_IRQHandler                    
00007f75  DAC0_IRQHandler                      
00007f75  DMA_IRQHandler                       
00007f75  Default_Handler                      
00007f75  GROUP0_IRQHandler                    
00007f75  HardFault_Handler                    
00007f75  I2C0_IRQHandler                      
00007f75  I2C1_IRQHandler                      
00007f75  NMI_Handler                          
00007f75  PendSV_Handler                       
00007f75  RTC_IRQHandler                       
00007f75  SPI0_IRQHandler                      
00007f75  SPI1_IRQHandler                      
00007f75  SVC_Handler                          
00007f75  TIMA0_IRQHandler                     
00007f75  TIMA1_IRQHandler                     
00007f75  TIMG0_IRQHandler                     
00007f75  TIMG12_IRQHandler                    
00007f75  TIMG6_IRQHandler                     
00007f75  TIMG7_IRQHandler                     
00007f75  TIMG8_IRQHandler                     
00007f75  UART0_IRQHandler                     
00007f75  UART1_IRQHandler                     
00007f75  UART2_IRQHandler                     
00007f75  UART3_IRQHandler                     
00007f78  C$$EXIT                              
00007f79  HOSTexit                             
00007f81  Reset_Handler                        
00007f95  _system_pre_init                     
00008b96  asc2_1608                            
00009186  asc2_0806                            
000093b0  __aeabi_ctype_table_                 
000093b0  __aeabi_ctype_table_C                
00009520  test                                 
00009548  reg                                  
00009618  hw                                   
000096d4  __TI_Handler_Table_Base              
000096e0  __TI_Handler_Table_Limit             
000096e8  __TI_CINIT_Base                      
000096f8  __TI_CINIT_Limit                     
000096f8  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  quat                                 
20200300  Data_Accel                           
20200306  Data_Gyro                            
2020030c  Data_Pitch                           
20200310  Data_Roll                            
20200314  Data_Yaw                             
20200318  ExISR_Flag                           
2020031c  sensor_timestamp                     
20200320  sensors                              
20200322  more                                 
20200324  Motor_Back_Left                      
20200364  Motor_Back_Right                     
202003a4  Motor_Font_Left                      
202003e4  Motor_Font_Right                     
20200450  Motor                                
20200487  Flag_LED                             
20200488  Data_MotorEncoder                    
20200490  Data_Tracker_Input                   
20200498  Data_Motor_TarSpeed                  
2020049c  Data_Tracker_Offset                  
202004a0  __aeabi_errno                        
202004a4  delayTick                            
202004a8  uwTick                               
202004ae  Flag_MPU6050_Ready                   
202004b1  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[311 symbols]
